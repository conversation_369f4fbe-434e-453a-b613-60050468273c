import React, { useState } from 'react';
import ConfirmationDialog from './ConfirmationDialog';
import { deleteStory, deleteDiscussion, deleteSupportRequest } from '../services/contentManagement';

/**
 * Content Actions Component
 * Provides delete functionality with confirmation for content items
 * Integrated with NAROOP 3-column layout design system
 */
export default function ContentActions({
  contentId,
  contentType = 'story',
  contentTitle = 'this item',
  onDelete,
  onEdit,
  showEdit = true,
  showDelete = true,
  className = '',
  size = 'medium' // 'small', 'medium', 'large'
}) {
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = () => {
    setShowDeleteConfirm(true);
  };

  const handleDeleteConfirm = async () => {
    setIsDeleting(true);
    try {
      // Use appropriate delete function based on content type
      let deleteFunction;
      switch (contentType) {
        case 'story':
          deleteFunction = deleteStory;
          break;
        case 'discussion':
          deleteFunction = deleteDiscussion;
          break;
        case 'support':
          deleteFunction = deleteSupportRequest;
          break;
        default:
          deleteFunction = deleteStory; // Default to story
      }

      await deleteFunction(contentId, 'current-user-id'); // In real app, get actual user ID
      if (onDelete) {
        onDelete(contentId);
      }
    } catch (error) {
      console.error('Error deleting content:', error);
      // Could show error notification here
    } finally {
      setIsDeleting(false);
      setShowDeleteConfirm(false);
    }
  };

  const handleEditClick = () => {
    if (onEdit) {
      onEdit(contentId);
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'small': return 'content-actions--small';
      case 'large': return 'content-actions--large';
      default: return 'content-actions--medium';
    }
  };

  return (
    <>
      <div className={`content-actions ${getSizeClass()} ${className}`}>
        {showEdit && (
          <button
            onClick={handleEditClick}
            className="content-action-btn content-action-btn--edit"
            title="Edit content"
            aria-label={`Edit ${contentType}`}
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
            </svg>
            {size === 'large' && <span>Edit</span>}
          </button>
        )}

        {showDelete && (
          <button
            onClick={handleDeleteClick}
            className="content-action-btn content-action-btn--delete"
            title="Delete content"
            aria-label={`Delete ${contentType}`}
            disabled={isDeleting}
          >
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <polyline points="3,6 5,6 21,6"></polyline>
              <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"></path>
              <line x1="10" y1="11" x2="10" y2="17"></line>
              <line x1="14" y1="11" x2="14" y2="17"></line>
            </svg>
            {size === 'large' && <span>{isDeleting ? 'Deleting...' : 'Delete'}</span>}
          </button>
        )}
      </div>

      <ConfirmationDialog
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={handleDeleteConfirm}
        title={`Delete ${contentType}`}
        message={`Are you sure you want to delete "${contentTitle}"? This action cannot be undone.`}
        confirmText="Delete"
        cancelText="Cancel"
        type="danger"
        requiresTyping={contentType === 'story'}
        confirmationPhrase={contentType === 'story' ? 'DELETE' : ''}
      />
    </>
  );
}

/**
 * Bulk Content Actions Component
 * For managing multiple content items at once
 */
export function BulkContentActions({
  selectedItems = [],
  onBulkDelete,
  onBulkEdit,
  onClearSelection,
  contentType = 'stories'
}) {
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);
  const [isBulkDeleting, setIsBulkDeleting] = useState(false);

  const handleBulkDelete = async () => {
    setIsBulkDeleting(true);
    try {
      for (const itemId of selectedItems) {
        await deleteContent(itemId, contentType.slice(0, -1)); // Remove 's' from plural
      }
      if (onBulkDelete) {
        onBulkDelete(selectedItems);
      }
      if (onClearSelection) {
        onClearSelection();
      }
    } catch (error) {
      console.error('Error bulk deleting content:', error);
    } finally {
      setIsBulkDeleting(false);
      setShowBulkDeleteConfirm(false);
    }
  };

  if (selectedItems.length === 0) {
    return null;
  }

  return (
    <>
      <div className="bulk-content-actions">
        <div className="bulk-actions-info">
          <span>{selectedItems.length} {contentType} selected</span>
        </div>

        <div className="bulk-actions-buttons">
          <button
            onClick={() => onClearSelection && onClearSelection()}
            className="bulk-action-btn bulk-action-btn--clear"
          >
            Clear Selection
          </button>

          {onBulkEdit && (
            <button
              onClick={() => onBulkEdit(selectedItems)}
              className="bulk-action-btn bulk-action-btn--edit"
            >
              Edit Selected
            </button>
          )}

          <button
            onClick={() => setShowBulkDeleteConfirm(true)}
            className="bulk-action-btn bulk-action-btn--delete"
            disabled={isBulkDeleting}
          >
            {isBulkDeleting ? 'Deleting...' : 'Delete Selected'}
          </button>
        </div>
      </div>

      <ConfirmationDialog
        isOpen={showBulkDeleteConfirm}
        onClose={() => setShowBulkDeleteConfirm(false)}
        onConfirm={handleBulkDelete}
        title={`Delete ${selectedItems.length} ${contentType}`}
        message={`Are you sure you want to delete ${selectedItems.length} ${contentType}? This action cannot be undone.`}
        confirmText="Delete All"
        cancelText="Cancel"
        type="danger"
        requiresTyping={true}
        confirmationPhrase="DELETE ALL"
      />
    </>
  );
}