/* NAROOP Mobile-First Responsive Design System */
/* Light Theme Responsive Design - No P<PERSON> Required */

/* Design Tokens for Responsive Design */
:root {
  /* Breakpoints */
  --breakpoint-mobile: 320px;
  --breakpoint-mobile-large: 480px;
  --breakpoint-tablet: 768px;
  --breakpoint-tablet-large: 1024px;
  --breakpoint-desktop: 1200px;
  --breakpoint-desktop-large: 1440px;
  
  /* Touch Targets */
  --touch-target-min: 44px;
  --touch-target-comfortable: 48px;
  --touch-target-large: 56px;
  
  /* Spacing Scale (Mobile-First) */
  --space-mobile-xs: 4px;
  --space-mobile-sm: 8px;
  --space-mobile-md: 12px;
  --space-mobile-lg: 16px;
  --space-mobile-xl: 24px;
  --space-mobile-2xl: 32px;
  
  /* Typography Scale (Mobile-First) */
  --text-mobile-xs: 12px;
  --text-mobile-sm: 14px;
  --text-mobile-base: 16px;
  --text-mobile-lg: 18px;
  --text-mobile-xl: 20px;
  --text-mobile-2xl: 24px;
  --text-mobile-3xl: 28px;
  
  /* Container Widths */
  --container-mobile: 100%;
  --container-tablet: 768px;
  --container-desktop: 1200px;
  --container-wide: 1440px;
}

/* Base Mobile-First Styles */
* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-tap-highlight-color: transparent;
}

body {
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background: var(--gradient-community);
  color: var(--color-heritage-black);
  overflow-x: hidden;
}

/* Touch-Friendly Interactive Elements */
button,
[role="button"],
input[type="submit"],
input[type="button"],
.touch-target {
  min-height: var(--touch-target-min);
  min-width: var(--touch-target-min);
  padding: var(--space-mobile-sm) var(--space-mobile-md);
  border-radius: 8px;
  border: 2px solid transparent;
  background: transparent;
  color: inherit;
  font-size: var(--text-mobile-base);
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-mobile-xs);
  text-decoration: none;
  position: relative;
}

/* Focus States for Touch Devices */
button:focus,
[role="button"]:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
  outline: 3px solid var(--color-heritage-gold);
  outline-offset: 2px;
}

/* Mobile Navigation Patterns */
.mobile-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--color-heritage-cream);
  border-top: 2px solid var(--color-heritage-gold);
  padding: var(--space-mobile-sm);
  z-index: 1000;
  box-shadow: 0 -4px 12px rgba(26, 26, 26, 0.15);
}

.mobile-nav__items {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: var(--container-mobile);
  margin: 0 auto;
}

.mobile-nav__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-mobile-xs);
  padding: var(--space-mobile-xs);
  min-width: var(--touch-target-comfortable);
  min-height: var(--touch-target-comfortable);
  border-radius: 8px;
  transition: all 0.3s ease;
  color: var(--color-heritage-forest);
  text-decoration: none;
}

.mobile-nav__item:hover,
.mobile-nav__item--active {
  background: var(--color-heritage-gold);
  color: var(--color-heritage-black);
}

.mobile-nav__icon {
  width: 24px;
  height: 24px;
}

.mobile-nav__label {
  font-size: var(--text-mobile-xs);
  font-weight: 600;
  text-align: center;
}

/* Mobile-First Grid System */
.responsive-grid {
  display: grid;
  gap: var(--space-mobile-md);
  padding: var(--space-mobile-md);
  width: 100%;
}

.responsive-grid--1 {
  grid-template-columns: 1fr;
}

.responsive-grid--2 {
  grid-template-columns: 1fr;
}

.responsive-grid--3 {
  grid-template-columns: 1fr;
}

.responsive-grid--4 {
  grid-template-columns: 1fr;
}

.responsive-grid--auto {
  grid-template-columns: 1fr;
}

/* Mobile-First Typography */
.responsive-text {
  font-size: var(--text-mobile-base);
  line-height: 1.6;
  margin-bottom: var(--space-mobile-md);
}

.responsive-heading-1 {
  font-size: var(--text-mobile-3xl);
  font-weight: 700;
  line-height: 1.2;
  margin: 0 0 var(--space-mobile-lg) 0;
  color: var(--color-heritage-black);
}

.responsive-heading-2 {
  font-size: var(--text-mobile-2xl);
  font-weight: 600;
  line-height: 1.3;
  margin: 0 0 var(--space-mobile-md) 0;
  color: var(--color-heritage-black);
}

.responsive-heading-3 {
  font-size: var(--text-mobile-xl);
  font-weight: 600;
  line-height: 1.4;
  margin: 0 0 var(--space-mobile-sm) 0;
  color: var(--color-heritage-black);
}

/* Mobile-First Forms */
.responsive-form {
  display: flex;
  flex-direction: column;
  gap: var(--space-mobile-md);
  width: 100%;
}

.responsive-input {
  width: 100%;
  min-height: var(--touch-target-comfortable);
  padding: var(--space-mobile-sm) var(--space-mobile-md);
  border: 2px solid var(--color-heritage-forest);
  border-radius: 8px;
  font-size: var(--text-mobile-base);
  background: var(--color-heritage-cream);
  color: var(--color-heritage-black);
  transition: all 0.3s ease;
}

.responsive-input:focus {
  border-color: var(--color-heritage-gold);
  box-shadow: 0 0 0 3px rgba(255, 215, 0, 0.3);
}

.responsive-textarea {
  min-height: 120px;
  resize: vertical;
  font-family: inherit;
}

/* Mobile-First Cards */
.responsive-card {
  background: var(--color-heritage-cream);
  border: 2px solid var(--color-heritage-forest);
  border-radius: 12px;
  padding: var(--space-mobile-md);
  box-shadow: 0 4px 12px rgba(26, 26, 26, 0.1);
  transition: all 0.3s ease;
}

.responsive-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(26, 26, 26, 0.15);
}

/* Tablet Responsive Styles */
@media (min-width: 768px) {
  :root {
    --space-md: var(--space-lg);
    --space-lg: var(--space-xl);
    --space-xl: var(--space-2xl);
    --text-base: 18px;
    --text-lg: 20px;
    --text-xl: 24px;
    --text-2xl: 28px;
    --text-3xl: 32px;
  }

  .responsive-grid {
    gap: var(--space-lg);
    padding: var(--space-lg);
  }

  .responsive-grid--2 {
    grid-template-columns: repeat(2, 1fr);
  }

  .responsive-grid--3 {
    grid-template-columns: repeat(2, 1fr);
  }

  .responsive-grid--4 {
    grid-template-columns: repeat(2, 1fr);
  }

  .responsive-grid--auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }

  .mobile-nav {
    position: static;
    background: var(--color-heritage-cream);
    border-top: none;
    border-bottom: 2px solid var(--color-heritage-gold);
    padding: var(--space-md);
  }

  .mobile-nav__items {
    justify-content: center;
    gap: var(--space-lg);
  }

  .mobile-nav__item {
    flex-direction: row;
    padding: var(--space-sm) var(--space-md);
    min-width: auto;
  }

  .mobile-nav__label {
    font-size: var(--text-base);
  }

  .responsive-form {
    max-width: 600px;
    margin: 0 auto;
  }
}

/* Desktop Responsive Styles */
@media (min-width: 1024px) {
  .responsive-grid {
    gap: var(--space-xl);
    padding: var(--space-xl);
  }

  .responsive-grid--3 {
    grid-template-columns: repeat(3, 1fr);
  }

  .responsive-grid--4 {
    grid-template-columns: repeat(4, 1fr);
  }

  .mobile-nav__items {
    justify-content: flex-start;
  }

  .responsive-form {
    max-width: 800px;
  }
}

/* Large Desktop Responsive Styles */
@media (min-width: 1200px) {
  .responsive-grid {
    max-width: var(--container-desktop);
    margin: 0 auto;
  }

  .responsive-grid--4 {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* Landscape Mobile Optimizations */
@media (max-height: 500px) and (orientation: landscape) {
  .mobile-nav {
    padding: var(--space-mobile-xs);
  }

  .mobile-nav__item {
    flex-direction: row;
    gap: var(--space-mobile-xs);
  }

  .mobile-nav__label {
    font-size: var(--text-mobile-xs);
  }

  .responsive-heading-1 {
    font-size: var(--text-mobile-2xl);
  }

  .responsive-heading-2 {
    font-size: var(--text-mobile-xl);
  }
}

/* High DPI Displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .responsive-card {
    box-shadow: 0 2px 8px rgba(26, 26, 26, 0.1);
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  button,
  .responsive-input,
  .responsive-card {
    border-width: 3px;
  }
}

/* Print Styles */
@media print {
  .mobile-nav,
  .responsive-grid {
    display: none;
  }

  .responsive-card {
    border: 1px solid black;
    box-shadow: none;
    break-inside: avoid;
  }
}

/* Utility Classes */
.hide-mobile {
  display: none;
}

.show-tablet {
  display: none;
}

.show-desktop {
  display: none;
}

@media (min-width: 768px) {
  .hide-mobile {
    display: block;
  }
  
  .show-tablet {
    display: block;
  }
  
  .hide-tablet {
    display: none;
  }
}

@media (min-width: 1024px) {
  .show-desktop {
    display: block;
  }
  
  .hide-desktop {
    display: none;
  }
}

/* Accessibility Helpers */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-heritage-yellow);
  color: var(--color-heritage-maroon);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 9999;
}

.skip-link:focus {
  top: 6px;
}

/* Enhanced Mobile Responsiveness without PWA */
@media (max-width: 768px) {
  /* Ensure all interactive elements meet touch target requirements */
  button,
  .btn,
  .action-btn,
  .quick-action-btn,
  .feed-tab,
  .story-card__action-btn,
  .naroop-reaction-btn,
  a[role="button"],
  input[type="submit"],
  input[type="button"] {
    min-height: 44px;
    min-width: 44px;
    padding: 12px 16px;
    margin: 4px;
  }

  /* Improve text readability on mobile */
  body,
  .dashboard-layout-root,
  .feed-item,
  .story-card {
    font-size: 16px;
    line-height: 1.5;
  }

  /* Ensure content doesn't overflow */
  .dashboard-layout-root,
  .feed-container,
  .story-card,
  .naroop-card {
    max-width: 100%;
    overflow-x: hidden;
  }

  /* Mobile-friendly spacing */
  .dashboard-layout-grid {
    padding: 1rem;
    gap: 1rem;
  }

  /* Improve form usability on mobile */
  input,
  textarea,
  select {
    font-size: 16px; /* Prevents zoom on iOS */
    padding: 12px;
    border-radius: 8px;
  }
}

/* Landscape orientation optimizations */
@media (max-width: 768px) and (orientation: landscape) {
  .dashboard-layout-root {
    padding: 0.5rem;
  }

  .dashboard-layout-grid {
    gap: 0.5rem;
  }
}
